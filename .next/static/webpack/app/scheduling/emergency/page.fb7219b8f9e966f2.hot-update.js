"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/scheduling/emergency/page",{

/***/ "(app-pages-browser)/./app/scheduling/emergency/page.tsx":
/*!*******************************************!*\
  !*** ./app/scheduling/emergency/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EmergencyDispatchPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,Clock,Loader2,MapPin,Phone,Truck,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _lib_hooks_useJobs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/hooks/useJobs */ \"(app-pages-browser)/./lib/hooks/useJobs.ts\");\n/* harmony import */ var _lib_hooks_useTeamMembers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useTeamMembers */ \"(app-pages-browser)/./lib/hooks/useTeamMembers.ts\");\n/* harmony import */ var _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useEquipment */ \"(app-pages-browser)/./lib/hooks/useEquipment.ts\");\n/* harmony import */ var _components_ui_job_form_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/job-form-modal */ \"(app-pages-browser)/./components/ui/job-form-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst emergencyJobs = [\n    {\n        id: 1,\n        title: \"Gas Leak Emergency\",\n        location: \"Main St & 5th Ave\",\n        priority: \"critical\",\n        reportedTime: \"10:45 AM\",\n        requiredSkills: [\n            \"Gas Certified\",\n            \"Emergency Response\"\n        ],\n        requiredEquipment: [\n            \"Emergency Vehicle\",\n            \"Gas Detection Equipment\"\n        ],\n        estimatedDuration: \"2-4 hours\",\n        contactPerson: \"Fire Department Chief\",\n        contactPhone: \"(*************\"\n    },\n    {\n        id: 2,\n        title: \"Water Line Break\",\n        location: \"Hospital District - Oak Ave\",\n        priority: \"urgent\",\n        reportedTime: \"11:20 AM\",\n        requiredSkills: [\n            \"Water Systems\",\n            \"Excavator Operation\"\n        ],\n        requiredEquipment: [\n            \"Excavator\",\n            \"Water Pump\"\n        ],\n        estimatedDuration: \"3-5 hours\",\n        contactPerson: \"Hospital Facilities Manager\",\n        contactPhone: \"(*************\"\n    }\n];\nconst activeJobs = [\n    {\n        id: 3,\n        title: \"Routine Maintenance\",\n        location: \"Park Ave\",\n        assignedPeople: [\n            \"Tom Wilson\"\n        ],\n        assignedEquipment: [\n            \"Service Truck #5\"\n        ],\n        impactIfReassigned: \"Will delay project by 2 hours\",\n        canReassign: true\n    },\n    {\n        id: 4,\n        title: \"Scheduled Installation\",\n        location: \"Oak St\",\n        assignedPeople: [\n            \"Jennifer Martinez\",\n            \"Robert Brown\"\n        ],\n        assignedEquipment: [\n            \"Excavator CAT 320\"\n        ],\n        impactIfReassigned: \"Will delay project by 4 hours\",\n        canReassign: true\n    },\n    {\n        id: 5,\n        title: \"Foundation Pour\",\n        location: \"Site Alpha\",\n        assignedPeople: [\n            \"Sarah Chen\",\n            \"Carlos Rodriguez\",\n            \"Mike Johnson\"\n        ],\n        assignedEquipment: [\n            \"Crane Liebherr 100\"\n        ],\n        impactIfReassigned: \"CRITICAL PROJECT - Cannot reassign\",\n        canReassign: false\n    }\n];\nfunction EmergencyDispatchPage() {\n    _s();\n    const [selectedEmergency, setSelectedEmergency] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCreateDialog, setShowCreateDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showJobModal, setShowJobModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showReassignDialog, setShowReassignDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [reassignmentData, setReassignmentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch live data\n    const { jobs, loading: jobsLoading, createJob, updateJob, refetch: refetchJobs } = (0,_lib_hooks_useJobs__WEBPACK_IMPORTED_MODULE_7__.useJobs)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000\n    });\n    const { teamMembers, loading: teamLoading } = (0,_lib_hooks_useTeamMembers__WEBPACK_IMPORTED_MODULE_8__.useTeamMembers)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000\n    });\n    const { equipment, loading: equipmentLoading } = (0,_lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment)({\n        organizationId: 'demo-org',\n        autoRefresh: true,\n        refreshInterval: 30000\n    });\n    // Filter emergency and urgent jobs\n    const emergencyJobsLive = jobs.filter((job)=>job.priority === 'urgent' && (job.status === 'scheduled' || job.status === 'in_progress'));\n    // Filter active jobs that can be reassigned\n    const activeJobsLive = jobs.filter((job)=>job.status === 'in_progress' && job.priority !== 'urgent').map((job)=>{\n        var _job_location, _job_schedule;\n        const assignedOperators = teamMembers.filter((member)=>{\n            var _job_assignments_operatorIds, _job_assignments;\n            return (_job_assignments = job.assignments) === null || _job_assignments === void 0 ? void 0 : (_job_assignments_operatorIds = _job_assignments.operatorIds) === null || _job_assignments_operatorIds === void 0 ? void 0 : _job_assignments_operatorIds.includes(member.id);\n        });\n        const assignedEquipment = equipment.filter((eq)=>{\n            var _job_assignments_equipmentIds, _job_assignments;\n            return (_job_assignments = job.assignments) === null || _job_assignments === void 0 ? void 0 : (_job_assignments_equipmentIds = _job_assignments.equipmentIds) === null || _job_assignments_equipmentIds === void 0 ? void 0 : _job_assignments_equipmentIds.includes(eq.id);\n        });\n        return {\n            id: job.id,\n            title: job.title,\n            location: ((_job_location = job.location) === null || _job_location === void 0 ? void 0 : _job_location.name) || 'Unknown Location',\n            assignedPeople: assignedOperators.map((op)=>\"\".concat(op.firstName, \" \").concat(op.lastName)),\n            assignedEquipment: assignedEquipment.map((eq)=>eq.name),\n            impactIfReassigned: \"Will delay \".concat(job.title, \" by \").concat(((_job_schedule = job.schedule) === null || _job_schedule === void 0 ? void 0 : _job_schedule.estimatedHours) || 2, \" hours\"),\n            canReassign: job.priority !== 'urgent',\n            originalJob: job\n        };\n    });\n    // Use live data or fallback to mock data\n    const emergencyJobsData = emergencyJobsLive.length > 0 ? emergencyJobsLive.map((job)=>{\n        var _job_location, _job_requirements, _job_requirements1, _job_schedule, _job_client, _job_client1;\n        return {\n            id: job.id,\n            title: job.title,\n            location: ((_job_location = job.location) === null || _job_location === void 0 ? void 0 : _job_location.name) || 'Unknown Location',\n            priority: job.priority === 'urgent' ? 'urgent' : 'critical',\n            reportedTime: job.createdAt ? typeof job.createdAt.toDate === 'function' ? new Date(job.createdAt.toDate()).toLocaleTimeString() : new Date(job.createdAt).toLocaleTimeString() : 'Unknown',\n            requiredSkills: ((_job_requirements = job.requirements) === null || _job_requirements === void 0 ? void 0 : _job_requirements.requiredSkills) || [],\n            requiredEquipment: ((_job_requirements1 = job.requirements) === null || _job_requirements1 === void 0 ? void 0 : _job_requirements1.requiredEquipmentTypes) || [],\n            estimatedDuration: ((_job_schedule = job.schedule) === null || _job_schedule === void 0 ? void 0 : _job_schedule.estimatedHours) ? \"\".concat(job.schedule.estimatedHours, \" hours\") : 'TBD',\n            contactPerson: ((_job_client = job.client) === null || _job_client === void 0 ? void 0 : _job_client.contactPerson) || 'Unknown',\n            contactPhone: ((_job_client1 = job.client) === null || _job_client1 === void 0 ? void 0 : _job_client1.phone) || 'Unknown',\n            originalJob: job\n        };\n    }) : emergencyJobs;\n    const activeJobsData = activeJobsLive.length > 0 ? activeJobsLive : activeJobs;\n    // Job creation handlers\n    const handleCreateEmergencyJob = async (jobData)=>{\n        try {\n            // Set emergency defaults\n            const emergencyJobData = {\n                ...jobData,\n                type: 'emergency',\n                priority: 'urgent',\n                status: 'scheduled'\n            };\n            await createJob(emergencyJobData);\n            setShowJobModal(false);\n            await refetchJobs();\n        } catch (error) {\n            console.error('Error creating emergency job:', error);\n            alert('Failed to create emergency job. Please try again.');\n        }\n    };\n    const handleReassign = (fromJob, toEmergency)=>{\n        setReassignmentData({\n            fromJob,\n            toEmergency\n        });\n        setShowReassignDialog(true);\n    };\n    const confirmReassignment = async ()=>{\n        if (!reassignmentData) return;\n        try {\n            const { fromJob, toEmergency } = reassignmentData;\n            // If we have live job data, update the assignments\n            if (fromJob.originalJob && toEmergency.originalJob) {\n                var _toEmergency_originalJob_assignments, _fromJob_originalJob_assignments, _toEmergency_originalJob_assignments1, _fromJob_originalJob_assignments1;\n                // Remove assignments from the original job\n                const updatedFromAssignments = {\n                    ...fromJob.originalJob.assignments,\n                    operatorIds: [],\n                    equipmentIds: []\n                };\n                // Add assignments to the emergency job\n                const updatedToAssignments = {\n                    ...toEmergency.originalJob.assignments,\n                    operatorIds: [\n                        ...((_toEmergency_originalJob_assignments = toEmergency.originalJob.assignments) === null || _toEmergency_originalJob_assignments === void 0 ? void 0 : _toEmergency_originalJob_assignments.operatorIds) || [],\n                        ...((_fromJob_originalJob_assignments = fromJob.originalJob.assignments) === null || _fromJob_originalJob_assignments === void 0 ? void 0 : _fromJob_originalJob_assignments.operatorIds) || []\n                    ],\n                    equipmentIds: [\n                        ...((_toEmergency_originalJob_assignments1 = toEmergency.originalJob.assignments) === null || _toEmergency_originalJob_assignments1 === void 0 ? void 0 : _toEmergency_originalJob_assignments1.equipmentIds) || [],\n                        ...((_fromJob_originalJob_assignments1 = fromJob.originalJob.assignments) === null || _fromJob_originalJob_assignments1 === void 0 ? void 0 : _fromJob_originalJob_assignments1.equipmentIds) || []\n                    ]\n                };\n                await updateJob(fromJob.originalJob.id, {\n                    assignments: updatedFromAssignments\n                });\n                await updateJob(toEmergency.originalJob.id, {\n                    assignments: updatedToAssignments\n                });\n                await refetchJobs();\n                alert(\"Successfully reassigned resources from \".concat(fromJob.title, \" to \").concat(toEmergency.title));\n            } else {\n                console.log(\"Reassigning resources:\", reassignmentData);\n                alert('Resource reassignment completed (mock mode)');\n            }\n            setShowReassignDialog(false);\n            setReassignmentData(null);\n        } catch (error) {\n            console.error('Error reassigning resources:', error);\n            alert('Failed to reassign resources. Please try again.');\n        }\n    };\n    // Show loading state\n    if (jobsLoading || teamLoading || equipmentLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-6 space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {\n                                className: \"text-gray-600 hover:bg-gray-100\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Emergency Dispatch\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Loading data...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarTrigger, {\n                                className: \"text-gray-600 hover:bg-gray-100\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Emergency Dispatch\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Quickly reassign resources for emergency situations\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 240,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>setShowJobModal(true),\n                        className: \"bg-red-600 hover:bg-red-700 text-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            \"CREATE EMERGENCY JOB\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2 text-red-800\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                \"Emergency Priority Queue (\",\n                                emergencyJobsData.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: emergencyJobsData.map((emergency)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-lg border-2 border-red-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-bold text-gray-900 text-lg\",\n                                                        children: emergency.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-4 text-sm text-gray-600 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                        lineNumber: 272,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    emergency.location\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Reported: \",\n                                                                    emergency.reportedTime\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                className: emergency.priority === \"critical\" ? \"bg-red-600 text-white\" : \"bg-orange-500 text-white\",\n                                                children: emergency.priority.toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Required Skills:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: emergency.requiredSkills.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: skill\n                                                            }, skill, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Required Equipment:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: emergency.requiredEquipment.map((equipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: equipment\n                                                            }, equipment, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Duration: \",\n                                                            emergency.estimatedDuration\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            emergency.contactPerson,\n                                                            \": \",\n                                                            emergency.contactPhone\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"sm\",\n                                                className: \"bg-red-600 hover:bg-red-700\",\n                                                onClick: ()=>setSelectedEmergency(emergency),\n                                                children: \"Assign Resources\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, emergency.id, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                \"Available for Reassignment\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: activeJobsData.map((job)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg \".concat(!job.canReassign ? \"bg-gray-50 opacity-60\" : \"hover:shadow-md\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900\",\n                                                        children: job.title\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-1 text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            job.location\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this),\n                                            job.canReassign ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                onClick: ()=>selectedEmergency && handleReassign(job, selectedEmergency),\n                                                disabled: !selectedEmergency,\n                                                children: \"Reassign to Emergency\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                children: \"Cannot Reassign\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Assigned People:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: job.assignedPeople.map((person)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    person\n                                                                ]\n                                                            }, person, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-medium text-gray-700 mb-1\",\n                                                        children: \"Assigned Equipment:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: job.assignedEquipment.map((equipment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                variant: \"secondary\",\n                                                                className: \"text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                        lineNumber: 385,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    equipment\n                                                                ]\n                                                            }, equipment, true, {\n                                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm \".concat(job.canReassign ? \"text-yellow-600\" : \"text-red-600\", \" font-medium\"),\n                                        children: [\n                                            \"Impact: \",\n                                            job.impactIfReassigned\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, job.id, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.Dialog, {\n                open: showReassignDialog,\n                onOpenChange: setShowReassignDialog,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_6__.DialogTitle, {\n                                className: \"flex items-center gap-2 text-red-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_Clock_Loader2_MapPin_Phone_Truck_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Confirm Resource Reassignment\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                lineNumber: 405,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this),\n                        reassignmentData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium text-yellow-800\",\n                                            children: \"Impact Assessment:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-700\",\n                                            children: reassignmentData.fromJob.impactIfReassigned\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"From:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                reassignmentData.fromJob.title,\n                                                \" (\",\n                                                reassignmentData.fromJob.location,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"To:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                reassignmentData.toEmergency.title,\n                                                \" (\",\n                                                reassignmentData.toEmergency.location,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Resources:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" \",\n                                                reassignmentData.fromJob.assignedPeople.join(\", \")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>setShowReassignDialog(false),\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                            lineNumber: 430,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            className: \"bg-red-600 hover:bg-red-700\",\n                                            onClick: confirmReassignment,\n                                            children: \"Confirm Reassignment\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                            lineNumber: 433,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_job_form_modal__WEBPACK_IMPORTED_MODULE_10__.JobFormModal, {\n                isOpen: showJobModal,\n                onClose: ()=>setShowJobModal(false),\n                onSubmit: handleCreateEmergencyJob,\n                job: null,\n                teamMembers: teamMembers,\n                equipment: equipment,\n                isLoading: false\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/dev/diggit-web/app/scheduling/emergency/page.tsx\",\n        lineNumber: 237,\n        columnNumber: 5\n    }, this);\n}\n_s(EmergencyDispatchPage, \"z5ZX4J72Li8D+XPa+cdF+0yi0Nw=\", false, function() {\n    return [\n        _lib_hooks_useJobs__WEBPACK_IMPORTED_MODULE_7__.useJobs,\n        _lib_hooks_useTeamMembers__WEBPACK_IMPORTED_MODULE_8__.useTeamMembers,\n        _lib_hooks_useEquipment__WEBPACK_IMPORTED_MODULE_9__.useEquipment\n    ];\n});\n_c = EmergencyDispatchPage;\nvar _c;\n$RefreshReg$(_c, \"EmergencyDispatchPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/scheduling/emergency/page.tsx\n"));

/***/ })

});