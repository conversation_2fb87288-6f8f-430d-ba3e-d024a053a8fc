import { NextResponse } from 'next/server'
import { initializeApp, getApps, cert } from 'firebase-admin/app'
import { getFirestore } from 'firebase-admin/firestore'

// Initialize Firebase Admin
function getFirebaseAdmin() {
  if (getApps().length > 0) {
    return getApps()[0]!
  }

  const firebaseAdminConfig = {
    credential: cert({
      projectId: 'diggit-web-sbx',
      clientEmail: '<EMAIL>',
      privateKey: `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
    }),
  }

  return initializeApp(firebaseAdminConfig)
}

export async function POST() {
  try {
    console.log('🌱 Starting data seeding...')
    
    const app = getFirebaseAdmin()
    const db = getFirestore(app)

    const orgId = 'demo-org'
    const timestamp = new Date().toISOString()

    // Create demo organization
    const orgData = {
      name: 'Demo Construction Company',
      slug: 'demo-construction',
      address: {
        street: '123 Construction Ave',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94105',
        country: 'USA'
      },
      subscription: {
        plan: 'pro',
        status: 'active',
        billingEmail: '<EMAIL>',
        maxUsers: 50,
        maxEquipment: 100
      },
      settings: {
        timezone: 'America/Los_Angeles',
        currency: 'USD',
        businessHours: {
          start: '07:00',
          end: '18:00',
          days: [1, 2, 3, 4, 5]
        },
        features: {
          emergencyDispatch: true,
          maintenanceAlerts: true,
          smartSuggestions: true,
          integrations: true
        }
      },
      createdAt: timestamp,
      updatedAt: timestamp,
      createdBy: 'seed-script'
    }

    console.log('🏢 Creating demo organization...')
    await db.collection('organizations').doc(orgId).set(orgData)

    // Create demo equipment
    const equipment = [
      {
        name: 'CAT 320 Excavator',
        type: 'excavator',
        make: 'Caterpillar',
        model: '320GC',
        year: 2022,
        vin: 'CAT320GC2022001',
        status: 'available',
        location: {
          name: 'Main Yard',
          address: '123 Construction Ave, San Francisco, CA',
          coordinates: {
            latitude: 37.7749,
            longitude: -122.4194
          },
          lastUpdated: timestamp
        },
        specifications: {
          weight: 45000,
          capacity: 2.5,
          fuelType: 'diesel',
          engineHours: 1250,
        },
        maintenance: {
          lastService: '2024-11-15T10:00:00Z',
          nextService: '2025-01-15T10:00:00Z',
          serviceIntervalHours: 250,
          alerts: []
        }
      },
      {
        name: 'John Deere 850K Dozer',
        type: 'bulldozer',
        make: 'John Deere',
        model: '850K',
        year: 2023,
        vin: 'JD850K2023001',
        status: 'in-use',
        location: {
          name: 'Highway 101 Project',
          address: 'Highway 101, Mile 23, San Mateo, CA',
          coordinates: {
            latitude: 37.5630,
            longitude: -122.3255
          },
          lastUpdated: timestamp
        },
        assignment: {
          jobId: 'job-highway-repair',
          operatorId: 'operator-mike-johnson',
          startTime: '2024-12-19T08:00:00Z',
          estimatedEndTime: '2024-12-19T17:00:00Z'
        },
        specifications: {
          weight: 38500,
          capacity: 3.8,
          fuelType: 'diesel',
          engineHours: 890,
        },
        maintenance: {
          lastService: '2024-12-01T09:00:00Z',
          nextService: '2025-02-01T09:00:00Z',
          serviceIntervalHours: 300,
          alerts: []
        }
      },
      {
        name: 'Volvo L120H Loader',
        type: 'loader',
        make: 'Volvo',
        model: 'L120H',
        year: 2021,
        vin: 'VOLVO120H2021001',
        status: 'maintenance',
        location: {
          name: 'Service Center',
          address: '456 Service Rd, Oakland, CA',
          coordinates: {
            latitude: 37.8044,
            longitude: -122.2711
          },
          lastUpdated: timestamp
        },
        specifications: {
          weight: 32000,
          capacity: 4.2,
          fuelType: 'diesel',
          engineHours: 2100,
        },
        maintenance: {
          lastService: timestamp,
          nextService: '2025-03-01T10:00:00Z',
          serviceIntervalHours: 250,
          alerts: ['Hydraulic fluid low', 'Scheduled maintenance due']
        }
      }
    ]

    console.log('🚛 Creating demo equipment...')
    for (const item of equipment) {
      const equipmentData = {
        ...item,
        organizationId: orgId,
        createdAt: timestamp,
        updatedAt: timestamp,
        createdBy: 'seed-script'
      }
      
      await db.collection('organizations').doc(orgId)
        .collection('equipment').add(equipmentData)
    }

    // Create demo team members
    const teamMembers = [
      {
        firstName: 'Mike',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '(*************',
        employeeId: 'EMP001',
        position: 'Heavy Equipment Operator',
        department: 'Operations',
        hireDate: '2022-03-15T00:00:00Z',
        status: 'active',
        skills: {
          'excavator-operation': {
            level: 'expert',
            certifiedAt: '2022-04-01T00:00:00Z',
            expiresAt: '2025-04-01T00:00:00Z',
            certificationNumber: 'EXC-2022-001'
          },
          'safety-training': {
            level: 'advanced',
            certifiedAt: '2024-01-15T00:00:00Z',
            expiresAt: '2025-01-15T00:00:00Z',
            certificationNumber: 'SAFE-2024-001'
          }
        },
        availability: {
          status: 'assigned',
          currentJobId: 'job-highway-repair',
          shift: {
            start: '07:00',
            end: '16:00'
          }
        }
      },
      {
        firstName: 'Sarah',
        lastName: 'Chen',
        email: '<EMAIL>',
        phone: '(*************',
        employeeId: 'EMP002',
        position: 'Site Supervisor',
        department: 'Operations',
        hireDate: '2021-08-10T00:00:00Z',
        status: 'active',
        skills: {
          'project-management': {
            level: 'expert',
            certifiedAt: '2021-09-01T00:00:00Z'
          },
          'safety-training': {
            level: 'expert',
            certifiedAt: '2024-01-15T00:00:00Z',
            expiresAt: '2025-01-15T00:00:00Z',
            certificationNumber: 'SAFE-2024-002'
          }
        },
        availability: {
          status: 'available',
          shift: {
            start: '06:00',
            end: '15:00'
          }
        }
      }
    ]

    console.log('👥 Creating demo team members...')
    for (const member of teamMembers) {
      const memberData = {
        ...member,
        organizationId: orgId,
        createdAt: timestamp,
        updatedAt: timestamp,
        createdBy: 'seed-script'
      }

      await db.collection('organizations').doc(orgId)
        .collection('teamMembers').add(memberData)
    }

    // Create demo jobs
    const jobs = [
      {
        title: 'Highway 101 Emergency Repair',
        description: 'Emergency pothole repair on Highway 101 near Mile Marker 23. High priority due to safety concerns.',
        type: 'emergency',
        status: 'in_progress',
        priority: 'urgent',
        client: {
          name: 'CalTrans',
          contactPerson: 'John Smith',
          phone: '(*************',
          email: '<EMAIL>'
        },
        location: {
          name: 'Highway 101, Mile Marker 23',
          address: 'Highway 101, Mile 23, San Mateo, CA',
          latitude: 37.5630,
          longitude: -122.3255,
          siteInstructions: 'Use north-bound shoulder for equipment staging. Traffic control required.'
        },
        schedule: {
          startDate: new Date('2024-12-20T08:00:00Z'),
          endDate: new Date('2024-12-20T17:00:00Z'),
          estimatedHours: 8,
          actualStartDate: new Date('2024-12-20T08:15:00Z')
        },
        budget: {
          estimated: 15000,
          currency: 'USD'
        },
        assignments: {
          teamLeadId: '',
          operatorIds: [],
          equipmentIds: []
        },
        requirements: {
          requiredSkills: ['Heavy Equipment Operation', 'Traffic Control', 'Emergency Response'],
          safetyRequirements: ['High-Vis Vest', 'Hard Hat', 'Safety Boots', 'Traffic Control Certification'],
          specialInstructions: 'Emergency response protocols in effect. Coordinate with CHP for traffic control.',
          minimumTeamSize: 3,
          requiredEquipmentTypes: ['excavator', 'truck']
        }
      },
      {
        title: 'Downtown Foundation Pour',
        description: 'Foundation pour for new commercial building downtown. Requires crane and concrete pump coordination.',
        type: 'construction',
        status: 'scheduled',
        priority: 'high',
        client: {
          name: 'Metro Development Corp',
          contactPerson: 'Lisa Rodriguez',
          phone: '(*************',
          email: '<EMAIL>'
        },
        location: {
          name: 'Metro Plaza Construction Site',
          address: '789 Market St, San Francisco, CA',
          latitude: 37.7849,
          longitude: -122.4094,
          siteInstructions: 'Access via rear alley. Coordinate with building security for site access.'
        },
        schedule: {
          startDate: new Date('2024-12-21T06:00:00Z'),
          endDate: new Date('2024-12-21T14:00:00Z'),
          estimatedHours: 8
        },
        budget: {
          estimated: 25000,
          currency: 'USD'
        },
        assignments: {
          teamLeadId: '',
          operatorIds: [],
          equipmentIds: []
        },
        requirements: {
          requiredSkills: ['Crane Operation', 'Concrete Work', 'Site Supervision'],
          safetyRequirements: ['Fall Protection', 'Hard Hat', 'Safety Boots', 'Crane Operation License'],
          specialInstructions: 'Coordinate with concrete supplier for 6 AM delivery. Weather contingency plan required.',
          minimumTeamSize: 4,
          requiredEquipmentTypes: ['crane', 'truck']
        }
      },
      {
        title: 'Routine Pipe Maintenance',
        description: 'Scheduled maintenance on water main infrastructure in residential area.',
        type: 'maintenance',
        status: 'completed',
        priority: 'medium',
        client: {
          name: 'SF Public Utilities',
          contactPerson: 'David Kim',
          phone: '(*************',
          email: '<EMAIL>'
        },
        location: {
          name: 'Sunset District - 19th Ave',
          address: '1900 19th Ave, San Francisco, CA',
          latitude: 37.7449,
          longitude: -122.4750,
          siteInstructions: 'Residential area - minimize noise before 8 AM and after 6 PM.'
        },
        schedule: {
          startDate: new Date('2024-12-19T08:00:00Z'),
          endDate: new Date('2024-12-19T12:00:00Z'),
          estimatedHours: 4,
          actualStartDate: new Date('2024-12-19T08:00:00Z'),
          actualEndDate: new Date('2024-12-19T11:30:00Z')
        },
        budget: {
          estimated: 8000,
          actual: 7500,
          currency: 'USD'
        },
        assignments: {
          teamLeadId: '',
          operatorIds: [],
          equipmentIds: []
        },
        requirements: {
          requiredSkills: ['Pipe Installation', 'Excavation', 'Water Systems'],
          safetyRequirements: ['Confined Space Training', 'Hard Hat', 'Safety Boots'],
          specialInstructions: 'Coordinate with residents for water shutoff notifications.',
          minimumTeamSize: 2,
          requiredEquipmentTypes: ['excavator']
        }
      }
    ]

    console.log('📋 Creating demo jobs...')
    for (const job of jobs) {
      const jobData = {
        ...job,
        organizationId: orgId,
        createdAt: timestamp,
        updatedAt: timestamp,
        createdBy: 'seed-script',
        isActive: true
      }

      await db.collection('organizations').doc(orgId)
        .collection('jobs').add(jobData)
    }

    console.log('✅ Demo data seeded successfully!')

    return NextResponse.json({
      success: true,
      message: 'Demo data seeded successfully! 🌱',
      data: {
        organization: orgData,
        equipmentCount: equipment.length,
        teamMemberCount: teamMembers.length,
        jobsCount: jobs.length
      },
      timestamp: timestamp
    })

  } catch (error) {
    console.error('❌ Error seeding data:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
