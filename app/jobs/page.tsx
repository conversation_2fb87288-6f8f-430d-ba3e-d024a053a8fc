"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { SidebarTrigger } from '@/components/ui/sidebar'
import { 
  Plus, 
  Search, 
  Filter, 
  MapPin, 
  Clock, 
  Users, 
  Truck, 
  Calendar,
  DollarSign,
  Loader2,
  Edit,
  Trash2
} from 'lucide-react'
import { useJobs } from '@/lib/hooks/useJobs'
import { useTeamMembers } from '@/lib/hooks/useTeamMembers'
import { useEquipment } from '@/lib/hooks/useEquipment'
import { JobFormModal } from '@/components/ui/job-form-modal'
import { Job, JobStatus, JobPriority, JobType } from '@/lib/types/firestore'

export default function JobsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<JobStatus | 'all'>('all')
  const [priorityFilter, setPriorityFilter] = useState<JobPriority | 'all'>('all')
  const [typeFilter, setTypeFilter] = useState<JobType | 'all'>('all')
  const [showJobModal, setShowJobModal] = useState(false)
  const [editingJob, setEditingJob] = useState<Job | null>(null)

  // Fetch live data
  const { jobs, loading: jobsLoading, createJob, updateJob, deleteJob, refetch } = useJobs({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  const { teamMembers, loading: teamLoading } = useTeamMembers({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  const { equipment, loading: equipmentLoading } = useEquipment({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Filter jobs based on search and filters
  const filteredJobs = jobs.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.location?.name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || job.status === statusFilter
    const matchesPriority = priorityFilter === 'all' || job.priority === priorityFilter
    const matchesType = typeFilter === 'all' || job.type === typeFilter

    return matchesSearch && matchesStatus && matchesPriority && matchesType
  })

  // Job form handlers
  const handleCreateJob = async (jobData: Partial<Job>) => {
    try {
      await createJob(jobData)
      setShowJobModal(false)
      await refetch()
    } catch (error) {
      console.error('Error creating job:', error)
      alert('Failed to create job. Please try again.')
    }
  }

  const handleEditJob = async (jobData: Partial<Job>) => {
    if (!editingJob) return
    
    try {
      await updateJob(editingJob.id, jobData)
      setEditingJob(null)
      setShowJobModal(false)
      await refetch()
    } catch (error) {
      console.error('Error updating job:', error)
      alert('Failed to update job. Please try again.')
    }
  }

  const handleDeleteJob = async (jobId: string) => {
    if (!confirm('Are you sure you want to delete this job?')) return
    
    try {
      await deleteJob(jobId)
      await refetch()
    } catch (error) {
      console.error('Error deleting job:', error)
      alert('Failed to delete job. Please try again.')
    }
  }

  const openEditModal = (job: Job) => {
    setEditingJob(job)
    setShowJobModal(true)
  }

  const getStatusColor = (status: JobStatus) => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-green-100 text-green-800'
      case 'on_hold': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-emerald-100 text-emerald-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: JobPriority) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800'
      case 'medium': return 'bg-blue-100 text-blue-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'urgent': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getAssignedTeamMembers = (job: Job) => {
    return teamMembers.filter(member => 
      job.assignments?.operatorIds?.includes(member.id)
    )
  }

  const getAssignedEquipment = (job: Job) => {
    return equipment.filter(eq => 
      job.assignments?.equipmentIds?.includes(eq.id)
    )
  }

  // Show loading state
  if (jobsLoading || teamLoading || equipmentLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Jobs Management</h1>
              <p className="text-gray-600">Loading jobs...</p>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Jobs Management</h1>
            <p className="text-gray-600">
              {filteredJobs.length} of {jobs.length} jobs
            </p>
          </div>
        </div>
        <Button 
          onClick={() => {
            setEditingJob(null)
            setShowJobModal(true)
          }}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Job
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as JobStatus | 'all')}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={priorityFilter} onValueChange={(value) => setPriorityFilter(value as JobPriority | 'all')}>
              <SelectTrigger>
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as JobType | 'all')}>
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="maintenance">Maintenance</SelectItem>
                <SelectItem value="construction">Construction</SelectItem>
                <SelectItem value="repair">Repair</SelectItem>
                <SelectItem value="inspection">Inspection</SelectItem>
                <SelectItem value="emergency">Emergency</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>

            <Button 
              variant="outline" 
              onClick={() => {
                setSearchTerm('')
                setStatusFilter('all')
                setPriorityFilter('all')
                setTypeFilter('all')
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Jobs List */}
      <div className="grid grid-cols-1 gap-4">
        {filteredJobs.map((job) => {
          const assignedTeam = getAssignedTeamMembers(job)
          const assignedEquipment = getAssignedEquipment(job)
          
          return (
            <Card key={job.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{job.title}</h3>
                    <p className="text-gray-600 text-sm mb-2">{job.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {job.location?.name || 'No location'}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {job.schedule?.startDate ?
                          (typeof job.schedule.startDate.toDate === 'function' ?
                            new Date(job.schedule.startDate.toDate()).toLocaleDateString() :
                            new Date(job.schedule.startDate).toLocaleDateString()
                          ) :
                          'No date'
                        }
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {job.schedule?.estimatedHours || 0}h estimated
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-3 w-3" />
                        ${job.budget?.estimated || 0}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(job.status)}>
                      {job.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                    <Badge className={getPriorityColor(job.priority)}>
                      {job.priority.toUpperCase()}
                    </Badge>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openEditModal(job)}
                    >
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteJob(job.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Assigned Team ({assignedTeam.length})</p>
                    <div className="flex flex-wrap gap-1">
                      {assignedTeam.length > 0 ? (
                        assignedTeam.map((member) => (
                          <Badge key={member.id} variant="secondary" className="text-xs">
                            <Users className="h-3 w-3 mr-1" />
                            {member.firstName} {member.lastName}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-xs text-gray-500">No team assigned</span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium text-gray-700 mb-1">Assigned Equipment ({assignedEquipment.length})</p>
                    <div className="flex flex-wrap gap-1">
                      {assignedEquipment.length > 0 ? (
                        assignedEquipment.map((eq) => (
                          <Badge key={eq.id} variant="secondary" className="text-xs">
                            <Truck className="h-3 w-3 mr-1" />
                            {eq.name}
                          </Badge>
                        ))
                      ) : (
                        <span className="text-xs text-gray-500">No equipment assigned</span>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {filteredJobs.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <p className="text-gray-500">No jobs found matching your criteria.</p>
            <Button 
              onClick={() => {
                setEditingJob(null)
                setShowJobModal(true)
              }}
              className="mt-4"
            >
              Create Your First Job
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Job Form Modal */}
      <JobFormModal
        isOpen={showJobModal}
        onClose={() => {
          setShowJobModal(false)
          setEditingJob(null)
        }}
        onSubmit={editingJob ? handleEditJob : handleCreateJob}
        job={editingJob}
        teamMembers={teamMembers}
        equipment={equipment}
        isLoading={false}
      />
    </div>
  )
}
