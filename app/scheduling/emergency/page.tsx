"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { AlertTriangle, Clock, MapPin, Users, Truck, Phone, ArrowRight, Plus, Loader2 } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useJobs } from "@/lib/hooks/useJobs"
import { useTeamMembers } from "@/lib/hooks/useTeamMembers"
import { useEquipment } from "@/lib/hooks/useEquipment"
import { JobFormModal } from "@/components/ui/job-form-modal"
import { Job, TeamMember, Equipment } from "@/lib/types/firestore"

const emergencyJobs = [
  {
    id: 1,
    title: "Gas Leak Emergency",
    location: "Main St & 5th Ave",
    priority: "critical",
    reportedTime: "10:45 AM",
    requiredSkills: ["Gas Certified", "Emergency Response"],
    requiredEquipment: ["Emergency Vehicle", "Gas Detection Equipment"],
    estimatedDuration: "2-4 hours",
    contactPerson: "Fire Department Chief",
    contactPhone: "(*************",
  },
  {
    id: 2,
    title: "Water Line Break",
    location: "Hospital District - Oak Ave",
    priority: "urgent",
    reportedTime: "11:20 AM",
    requiredSkills: ["Water Systems", "Excavator Operation"],
    requiredEquipment: ["Excavator", "Water Pump"],
    estimatedDuration: "3-5 hours",
    contactPerson: "Hospital Facilities Manager",
    contactPhone: "(*************",
  },
]

const activeJobs = [
  {
    id: 3,
    title: "Routine Maintenance",
    location: "Park Ave",
    assignedPeople: ["Tom Wilson"],
    assignedEquipment: ["Service Truck #5"],
    impactIfReassigned: "Will delay project by 2 hours",
    canReassign: true,
  },
  {
    id: 4,
    title: "Scheduled Installation",
    location: "Oak St",
    assignedPeople: ["Jennifer Martinez", "Robert Brown"],
    assignedEquipment: ["Excavator CAT 320"],
    impactIfReassigned: "Will delay project by 4 hours",
    canReassign: true,
  },
  {
    id: 5,
    title: "Foundation Pour",
    location: "Site Alpha",
    assignedPeople: ["Sarah Chen", "Carlos Rodriguez", "Mike Johnson"],
    assignedEquipment: ["Crane Liebherr 100"],
    impactIfReassigned: "CRITICAL PROJECT - Cannot reassign",
    canReassign: false,
  },
]

export default function EmergencyDispatchPage() {
  const [selectedEmergency, setSelectedEmergency] = useState<any>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showJobModal, setShowJobModal] = useState(false)
  const [showReassignDialog, setShowReassignDialog] = useState(false)
  const [reassignmentData, setReassignmentData] = useState<any>(null)

  // Fetch live data
  const { jobs, loading: jobsLoading, createJob, updateJob, refetch: refetchJobs } = useJobs({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  const { teamMembers, loading: teamLoading } = useTeamMembers({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  const { equipment, loading: equipmentLoading } = useEquipment({
    organizationId: 'demo-org',
    autoRefresh: true,
    refreshInterval: 30000
  })

  // Filter emergency and urgent jobs
  const emergencyJobsLive = jobs.filter(job =>
    job.priority === 'urgent' && (job.status === 'scheduled' || job.status === 'in_progress')
  )

  // Filter active jobs that can be reassigned
  const activeJobsLive = jobs.filter(job =>
    job.status === 'in_progress' && job.priority !== 'urgent'
  ).map(job => {
    const assignedOperators = teamMembers.filter(member =>
      job.assignments?.operatorIds?.includes(member.id)
    )
    const assignedEquipment = equipment.filter(eq =>
      job.assignments?.equipmentIds?.includes(eq.id)
    )

    return {
      id: job.id,
      title: job.title,
      location: job.location?.name || 'Unknown Location',
      assignedPeople: assignedOperators.map(op => `${op.firstName} ${op.lastName}`),
      assignedEquipment: assignedEquipment.map(eq => eq.name),
      impactIfReassigned: `Will delay ${job.title} by ${job.schedule?.estimatedHours || 2} hours`,
      canReassign: job.priority !== 'urgent',
      originalJob: job
    }
  })

  // Use live data or fallback to mock data
  const emergencyJobsData = emergencyJobsLive.length > 0 ? emergencyJobsLive.map(job => ({
    id: job.id,
    title: job.title,
    location: job.location?.name || 'Unknown Location',
    priority: job.priority === 'urgent' ? 'urgent' : 'critical',
    reportedTime: job.createdAt ? new Date(job.createdAt.toDate()).toLocaleTimeString() : 'Unknown',
    requiredSkills: job.requirements?.requiredSkills || [],
    requiredEquipment: job.requirements?.requiredEquipmentTypes || [],
    estimatedDuration: job.schedule?.estimatedHours ? `${job.schedule.estimatedHours} hours` : 'TBD',
    contactPerson: job.client?.contactPerson || 'Unknown',
    contactPhone: job.client?.phone || 'Unknown',
    originalJob: job
  })) : emergencyJobs

  const activeJobsData = activeJobsLive.length > 0 ? activeJobsLive : activeJobs

  // Job creation handlers
  const handleCreateEmergencyJob = async (jobData: Partial<Job>) => {
    try {
      // Set emergency defaults
      const emergencyJobData = {
        ...jobData,
        type: 'emergency' as const,
        priority: 'urgent' as const,
        status: 'scheduled' as const
      }

      await createJob(emergencyJobData)
      setShowJobModal(false)
      await refetchJobs()
    } catch (error) {
      console.error('Error creating emergency job:', error)
      alert('Failed to create emergency job. Please try again.')
    }
  }

  const handleReassign = (fromJob: any, toEmergency: any) => {
    setReassignmentData({ fromJob, toEmergency })
    setShowReassignDialog(true)
  }

  const confirmReassignment = async () => {
    if (!reassignmentData) return

    try {
      const { fromJob, toEmergency } = reassignmentData

      // If we have live job data, update the assignments
      if (fromJob.originalJob && toEmergency.originalJob) {
        // Remove assignments from the original job
        const updatedFromAssignments = {
          ...fromJob.originalJob.assignments,
          operatorIds: [],
          equipmentIds: []
        }

        // Add assignments to the emergency job
        const updatedToAssignments = {
          ...toEmergency.originalJob.assignments,
          operatorIds: [...(toEmergency.originalJob.assignments?.operatorIds || []), ...(fromJob.originalJob.assignments?.operatorIds || [])],
          equipmentIds: [...(toEmergency.originalJob.assignments?.equipmentIds || []), ...(fromJob.originalJob.assignments?.equipmentIds || [])]
        }

        await updateJob(fromJob.originalJob.id, { assignments: updatedFromAssignments })
        await updateJob(toEmergency.originalJob.id, { assignments: updatedToAssignments })

        await refetchJobs()
        alert(`Successfully reassigned resources from ${fromJob.title} to ${toEmergency.title}`)
      } else {
        console.log("Reassigning resources:", reassignmentData)
        alert('Resource reassignment completed (mock mode)')
      }

      setShowReassignDialog(false)
      setReassignmentData(null)
    } catch (error) {
      console.error('Error reassigning resources:', error)
      alert('Failed to reassign resources. Please try again.')
    }
  }

  // Show loading state
  if (jobsLoading || teamLoading || equipmentLoading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Emergency Dispatch</h1>
              <p className="text-gray-600">Loading data...</p>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <SidebarTrigger className="text-gray-600 hover:bg-gray-100" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Emergency Dispatch</h1>
            <p className="text-gray-600">Quickly reassign resources for emergency situations</p>
          </div>
        </div>
        <Button
          onClick={() => setShowJobModal(true)}
          className="bg-red-600 hover:bg-red-700 text-white"
        >
          <AlertTriangle className="h-4 w-4 mr-2" />
          CREATE EMERGENCY JOB
        </Button>
      </div>

      {/* Emergency Queue */}
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            Emergency Priority Queue ({emergencyJobsData.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {emergencyJobsData.map((emergency) => (
            <div key={emergency.id} className="bg-white p-4 rounded-lg border-2 border-red-200">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-bold text-gray-900 text-lg">{emergency.title}</h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {emergency.location}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      Reported: {emergency.reportedTime}
                    </div>
                  </div>
                </div>
                <Badge
                  className={emergency.priority === "critical" ? "bg-red-600 text-white" : "bg-orange-500 text-white"}
                >
                  {emergency.priority.toUpperCase()}
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Required Skills:</p>
                  <div className="flex flex-wrap gap-1">
                    {emergency.requiredSkills.map((skill) => (
                      <Badge key={skill} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Required Equipment:</p>
                  <div className="flex flex-wrap gap-1">
                    {emergency.requiredEquipment.map((equipment) => (
                      <Badge key={equipment} variant="outline" className="text-xs">
                        {equipment}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>Duration: {emergency.estimatedDuration}</span>
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {emergency.contactPerson}: {emergency.contactPhone}
                  </div>
                </div>
                <Button
                  size="sm"
                  className="bg-red-600 hover:bg-red-700"
                  onClick={() => setSelectedEmergency(emergency)}
                >
                  Assign Resources
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Available for Reassignment */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ArrowRight className="h-5 w-5 text-blue-600" />
            Available for Reassignment
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {activeJobsData.map((job) => (
            <div
              key={job.id}
              className={`p-4 border rounded-lg ${!job.canReassign ? "bg-gray-50 opacity-60" : "hover:shadow-md"}`}
            >
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h4 className="font-semibold text-gray-900">{job.title}</h4>
                  <div className="flex items-center gap-1 text-sm text-gray-600">
                    <MapPin className="h-3 w-3" />
                    {job.location}
                  </div>
                </div>
                {job.canReassign ? (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => selectedEmergency && handleReassign(job, selectedEmergency)}
                    disabled={!selectedEmergency}
                  >
                    Reassign to Emergency
                  </Button>
                ) : (
                  <Badge variant="secondary">Cannot Reassign</Badge>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Assigned People:</p>
                  <div className="flex flex-wrap gap-1">
                    {job.assignedPeople.map((person) => (
                      <Badge key={person} variant="secondary" className="text-xs">
                        <Users className="h-3 w-3 mr-1" />
                        {person}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700 mb-1">Assigned Equipment:</p>
                  <div className="flex flex-wrap gap-1">
                    {job.assignedEquipment.map((equipment) => (
                      <Badge key={equipment} variant="secondary" className="text-xs">
                        <Truck className="h-3 w-3 mr-1" />
                        {equipment}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className={`text-sm ${job.canReassign ? "text-yellow-600" : "text-red-600"} font-medium`}>
                Impact: {job.impactIfReassigned}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={showReassignDialog} onOpenChange={setShowReassignDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Confirm Resource Reassignment
            </DialogTitle>
          </DialogHeader>
          {reassignmentData && (
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="font-medium text-yellow-800">Impact Assessment:</p>
                <p className="text-yellow-700">{reassignmentData.fromJob.impactIfReassigned}</p>
              </div>

              <div className="space-y-2">
                <p>
                  <strong>From:</strong> {reassignmentData.fromJob.title} ({reassignmentData.fromJob.location})
                </p>
                <p>
                  <strong>To:</strong> {reassignmentData.toEmergency.title} ({reassignmentData.toEmergency.location})
                </p>
                <p>
                  <strong>Resources:</strong> {reassignmentData.fromJob.assignedPeople.join(", ")}
                </p>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowReassignDialog(false)}>
                  Cancel
                </Button>
                <Button className="bg-red-600 hover:bg-red-700" onClick={confirmReassignment}>
                  Confirm Reassignment
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Emergency Job Form Modal */}
      <JobFormModal
        isOpen={showJobModal}
        onClose={() => setShowJobModal(false)}
        onSubmit={handleCreateEmergencyJob}
        job={null}
        teamMembers={teamMembers}
        equipment={equipment}
        isLoading={false}
      />
    </div>
  )
}
