import { Timestamp } from 'firebase/firestore'

// Base interface for all Firestore documents
export interface FirestoreDocument {
  id: string
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Organization/Company types
export interface Organization extends FirestoreDocument {
  name: string
  type: 'construction' | 'utilities' | 'municipal' | 'other'
  address: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
  contactInfo: {
    phone: string
    email: string
    website?: string
  }
  settings: {
    timezone: string
    currency: string
    businessHours: {
      start: string // HH:mm format
      end: string // HH:mm format
      workDays: number[] // 0-6, Sunday=0
    }
  }
  subscription: {
    plan: 'free' | 'basic' | 'professional' | 'enterprise'
    status: 'active' | 'inactive' | 'suspended'
    expiresAt?: Timestamp
  }
  isActive: boolean
}

// User/Team Member types
export type UserRole = 'admin' | 'manager' | 'dispatcher' | 'operator' | 'viewer'

export interface User extends FirestoreDocument {
  organizationId: string
  email: string
  firstName: string
  lastName: string
  role: UserRole
  permissions: string[]
  isActive: boolean
  lastLoginAt?: Timestamp
  profile: {
    phone?: string
    emergencyContact?: {
      name: string
      phone: string
      relationship: string
    }
    certifications?: string[]
    licenseNumber?: string
    dateOfBirth?: Timestamp
    hireDate?: Timestamp
  }
}

// Team Member types (for operational staff)
export type TeamMemberStatus = 'active' | 'inactive' | 'on-leave' | 'terminated'
export type AvailabilityStatus = 'available' | 'assigned' | 'busy' | 'off-duty' | 'unavailable'
export type SkillLevel = 'beginner' | 'intermediate' | 'advanced' | 'expert'

export interface TeamMemberSkill {
  level: SkillLevel
  certifiedAt?: Timestamp
  expiresAt?: Timestamp
  certificationNumber?: string
  isVerified?: boolean
}

export interface TeamMemberAvailability {
  status: AvailabilityStatus
  currentJobId?: string
  currentLocation?: {
    name: string
    coordinates?: {
      latitude: number
      longitude: number
    }
  }
  shift?: {
    start: string // HH:MM format
    end: string   // HH:MM format
  }
  notes?: string
  lastUpdated?: Timestamp
}

export interface TeamMember extends FirestoreDocument {
  organizationId: string

  // Personal Information
  firstName: string
  lastName: string
  email?: string
  phone?: string
  employeeId?: string

  // Employment Details
  position: string
  department: string
  hireDate: Timestamp
  status: TeamMemberStatus

  // Skills & Certifications
  skills: {
    [skillId: string]: TeamMemberSkill
  }

  // Current Availability
  availability: TeamMemberAvailability

  // Emergency Contact
  emergencyContact?: {
    name: string
    phone: string
    relationship: string
  }

  // Additional Profile Data
  profile?: {
    avatar?: string
    dateOfBirth?: Timestamp
    address?: string
    notes?: string
  }
}

// Equipment/Vehicle types
export type EquipmentType = 'excavator' | 'dump_truck' | 'backhoe' | 'bulldozer' | 'crane' | 'compactor' | 'loader' | 'grader' | 'skid_steer' | 'utility_truck' | 'other'
export type EquipmentStatus = 'available' | 'in_use' | 'maintenance' | 'repair' | 'out_of_service'

export interface Equipment extends FirestoreDocument {
  organizationId: string
  name: string
  type: EquipmentType
  make: string
  model: string
  year: number
  serialNumber: string
  licensePlate?: string
  vin?: string
  status: EquipmentStatus
  currentLocation?: {
    latitude: number
    longitude: number
    address?: string
    lastUpdated: Timestamp
  }
  specifications: {
    capacity?: string
    weight?: number
    dimensions?: {
      length: number
      width: number
      height: number
    }
    engineType?: string
    fuelType: 'diesel' | 'gasoline' | 'electric' | 'hybrid'
    fuelCapacity?: number
  }
  maintenance: {
    lastServiceDate?: Timestamp
    nextServiceDue?: Timestamp
    serviceInterval: number // hours
    currentHours: number
    warrantyExpiration?: Timestamp
  }
  assignments: {
    currentJobId?: string
    currentOperatorId?: string
    assignedAt?: Timestamp
  }
  isActive: boolean
}

// Job/Project types
export type JobStatus = 'draft' | 'scheduled' | 'in_progress' | 'on_hold' | 'completed' | 'cancelled'
export type JobPriority = 'low' | 'medium' | 'high' | 'urgent'
export type JobType = 'maintenance' | 'construction' | 'repair' | 'inspection' | 'emergency' | 'other'

export interface JobAssignment {
  teamLeadId?: string
  operatorIds: string[]
  equipmentIds: string[]
  assignedAt?: Timestamp
  assignedBy?: string
}

export interface JobRequirements {
  requiredSkills: string[]
  safetyRequirements: string[]
  specialInstructions?: string
  minimumTeamSize?: number
  requiredEquipmentTypes?: string[]
}

export interface JobSchedule {
  startDate: Timestamp
  endDate: Timestamp
  estimatedHours: number
  actualHours?: number
  actualStartDate?: Timestamp
  actualEndDate?: Timestamp
}

export interface JobLocation {
  name: string
  address?: string
  latitude?: number
  longitude?: number
  siteInstructions?: string
}

export interface JobClient {
  name: string
  contactPerson: string
  phone: string
  email: string
}

export interface JobBudget {
  estimated: number
  actual?: number
  currency: string
}

export interface Job extends FirestoreDocument {
  organizationId: string
  projectId?: string
  title: string
  description: string
  type: JobType
  status: JobStatus
  priority: JobPriority
  client: JobClient
  location: JobLocation
  schedule: JobSchedule
  budget: JobBudget
  assignments: JobAssignment
  requirements: JobRequirements
  progress: {
    percentComplete: number
    milestonesCompleted: string[]
    nextMilestone?: string
  }
  isActive: boolean
}

// Project types (for larger initiatives)
export interface Project extends FirestoreDocument {
  organizationId: string
  name: string
  description: string
  status: 'planning' | 'active' | 'on_hold' | 'completed' | 'cancelled'
  client: {
    name: string
    contactPerson: string
    phone: string
    email: string
  }
  budget: {
    total: number
    spent: number
    currency: string
  }
  timeline: {
    startDate: Timestamp
    endDate: Timestamp
    actualStartDate?: Timestamp
    actualEndDate?: Timestamp
  }
  teamSize: number
  jobIds: string[] // References to related jobs
  isActive: boolean
}

// Maintenance Record types
export type MaintenanceType = 'routine' | 'repair' | 'inspection' | 'emergency'
export type MaintenanceStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled'

export interface MaintenanceRecord extends FirestoreDocument {
  organizationId: string
  equipmentId: string
  type: MaintenanceType
  status: MaintenanceStatus
  title: string
  description: string
  scheduledDate: Timestamp
  completedDate?: Timestamp
  performedBy?: string
  cost?: number
  currency?: string
  parts?: {
    name: string
    quantity: number
    cost: number
  }[]
  notes?: string
  nextServiceDue?: Timestamp
}

// Activity Log types
export type ActivityType = 'equipment_assignment' | 'job_update' | 'maintenance' | 'user_action' | 'system_event'

export interface ActivityLog extends FirestoreDocument {
  organizationId: string
  type: ActivityType
  entityType: 'user' | 'equipment' | 'job' | 'project' | 'maintenance'
  entityId: string
  userId?: string
  action: string
  description: string
  metadata?: Record<string, any>
  ipAddress?: string
}

// Notification types
export interface Notification extends FirestoreDocument {
  organizationId: string
  userId: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  read: boolean
  actionRequired: boolean
  actionUrl?: string
  expiresAt?: Timestamp
}

// Dashboard/Analytics types
export interface DashboardMetrics {
  organizationId: string
  date: string // YYYY-MM-DD format
  metrics: {
    fleetUtilization: {
      total: number
      active: number
      percentage: number
    }
    equipmentStatus: {
      available: number
      inUse: number
      maintenance: number
      outOfService: number
    }
    jobs: {
      active: number
      completed: number
      scheduled: number
    }
    teamMembers: {
      active: number
      total: number
    }
    costs: {
      fuel: number
      maintenance: number
      labor: number
      total: number
      currency: string
    }
  }
  updatedAt: Timestamp
}

// Export utility types
export type OrganizationRole = 'owner' | 'admin' | 'member'

export interface UserOrganization {
  userId: string
  organizationId: string
  role: OrganizationRole
  joinedAt: Timestamp
  isActive: boolean
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginationOptions {
  limit?: number
  offset?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    total: number
    limit: number
    offset: number
    hasMore: boolean
  }
}