"use client"

import { useState, useEffect, useCallback } from 'react'
import { 
  collection, 
  query, 
  orderBy, 
  onSnapshot, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs,
  where,
  Timestamp 
} from 'firebase/firestore'
import { db } from '@/lib/firebase/config'
import { Job, JobStatus, JobPriority, JobType } from '@/lib/types/firestore'

interface UseJobsOptions {
  organizationId?: string
  autoRefresh?: boolean
  refreshInterval?: number
  status?: JobStatus
  priority?: JobPriority
  type?: JobType
}

interface UseJobsReturn {
  jobs: Job[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createJob: (data: Partial<Job>) => Promise<Job | null>
  updateJob: (id: string, data: Partial<Job>) => Promise<Job | null>
  deleteJob: (id: string) => Promise<boolean>
  getJobsByStatus: (status: JobStatus) => Job[]
  getJobsByPriority: (priority: JobPriority) => Job[]
  getActiveJobs: () => Job[]
  getScheduledJobs: () => Job[]
  getEmergencyJobs: () => Job[]
}

export function useJobs(options: UseJobsOptions = {}): UseJobsReturn {
  const {
    organizationId = 'demo-org',
    autoRefresh = false,
    refreshInterval = 30000, // 30 seconds
    status,
    priority,
    type
  } = options

  const [jobs, setJobs] = useState<Job[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Build query based on filters
  const buildQuery = useCallback(() => {
    const jobsRef = collection(db, 'organizations', organizationId, 'jobs')
    let q = query(jobsRef, orderBy('createdAt', 'desc'))

    if (status) {
      q = query(jobsRef, where('status', '==', status), orderBy('createdAt', 'desc'))
    }
    if (priority) {
      q = query(jobsRef, where('priority', '==', priority), orderBy('createdAt', 'desc'))
    }
    if (type) {
      q = query(jobsRef, where('type', '==', type), orderBy('createdAt', 'desc'))
    }

    return q
  }, [organizationId, status, priority, type])

  // Fetch jobs data
  const fetchJobs = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const q = buildQuery()
      const querySnapshot = await getDocs(q)
      
      const jobsData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Job[]

      setJobs(jobsData)
    } catch (err) {
      console.error('Error fetching jobs:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch jobs')
    } finally {
      setLoading(false)
    }
  }, [buildQuery])

  // Set up real-time listener
  useEffect(() => {
    if (!autoRefresh) {
      fetchJobs()
      return
    }

    const q = buildQuery()
    const unsubscribe = onSnapshot(q, 
      (querySnapshot) => {
        const jobsData = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as Job[]
        
        setJobs(jobsData)
        setLoading(false)
        setError(null)
      },
      (err) => {
        console.error('Error in jobs listener:', err)
        setError(err.message)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [autoRefresh, buildQuery, fetchJobs])

  // Auto-refresh interval
  useEffect(() => {
    if (!autoRefresh || refreshInterval <= 0) return

    const interval = setInterval(fetchJobs, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchJobs])

  // Create new job
  const createJob = useCallback(async (data: Partial<Job>): Promise<Job | null> => {
    try {
      const jobsRef = collection(db, 'organizations', organizationId, 'jobs')
      
      const newJob = {
        organizationId,
        title: data.title || '',
        description: data.description || '',
        type: data.type || 'other' as JobType,
        status: data.status || 'draft' as JobStatus,
        priority: data.priority || 'medium' as JobPriority,
        client: data.client || {
          name: '',
          contactPerson: '',
          phone: '',
          email: ''
        },
        location: data.location || {
          name: '',
          address: ''
        },
        schedule: data.schedule || {
          startDate: Timestamp.now(),
          endDate: Timestamp.now(),
          estimatedHours: 0
        },
        budget: data.budget || {
          estimated: 0,
          currency: 'USD'
        },
        assignments: data.assignments || {
          operatorIds: [],
          equipmentIds: []
        },
        requirements: data.requirements || {
          requiredSkills: [],
          safetyRequirements: []
        },
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        isActive: true,
        ...data
      }

      const docRef = await addDoc(jobsRef, newJob)
      const createdJob = { id: docRef.id, ...newJob } as Job
      
      // Update local state
      setJobs(prev => [createdJob, ...prev])
      
      return createdJob
    } catch (err) {
      console.error('Error creating job:', err)
      setError(err instanceof Error ? err.message : 'Failed to create job')
      return null
    }
  }, [organizationId])

  // Update existing job
  const updateJob = useCallback(async (id: string, data: Partial<Job>): Promise<Job | null> => {
    try {
      const jobRef = doc(db, 'organizations', organizationId, 'jobs', id)
      
      const updateData = {
        ...data,
        updatedAt: Timestamp.now()
      }
      
      await updateDoc(jobRef, updateData)
      
      // Update local state
      setJobs(prev => prev.map(job => 
        job.id === id ? { ...job, ...updateData } : job
      ))
      
      const updatedJob = jobs.find(job => job.id === id)
      return updatedJob ? { ...updatedJob, ...updateData } : null
    } catch (err) {
      console.error('Error updating job:', err)
      setError(err instanceof Error ? err.message : 'Failed to update job')
      return null
    }
  }, [organizationId, jobs])

  // Delete job
  const deleteJob = useCallback(async (id: string): Promise<boolean> => {
    try {
      const jobRef = doc(db, 'organizations', organizationId, 'jobs', id)
      await deleteDoc(jobRef)
      
      // Update local state
      setJobs(prev => prev.filter(job => job.id !== id))
      
      return true
    } catch (err) {
      console.error('Error deleting job:', err)
      setError(err instanceof Error ? err.message : 'Failed to delete job')
      return false
    }
  }, [organizationId])

  // Helper functions for filtering jobs
  const getJobsByStatus = useCallback((status: JobStatus) => {
    return jobs.filter(job => job.status === status)
  }, [jobs])

  const getJobsByPriority = useCallback((priority: JobPriority) => {
    return jobs.filter(job => job.priority === priority)
  }, [jobs])

  const getActiveJobs = useCallback(() => {
    return jobs.filter(job => job.status === 'in_progress')
  }, [jobs])

  const getScheduledJobs = useCallback(() => {
    return jobs.filter(job => job.status === 'scheduled')
  }, [jobs])

  const getEmergencyJobs = useCallback(() => {
    return jobs.filter(job => job.priority === 'urgent' && job.status !== 'completed')
  }, [jobs])

  return {
    jobs,
    loading,
    error,
    refetch: fetchJobs,
    createJob,
    updateJob,
    deleteJob,
    getJobsByStatus,
    getJobsByPriority,
    getActiveJobs,
    getScheduledJobs,
    getEmergencyJobs
  }
}
