"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, <PERSON><PERSON>Title, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { X, Plus } from 'lucide-react'
import { Job, JobStatus, JobPriority, JobType, TeamMember, Equipment } from '@/lib/types/firestore'
import { Timestamp } from 'firebase/firestore'

interface JobFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: Partial<Job>) => Promise<void>
  job?: Job | null
  isLoading?: boolean
  teamMembers?: TeamMember[]
  equipment?: Equipment[]
}

export function JobFormModal({
  isOpen,
  onClose,
  onSubmit,
  job,
  isLoading = false,
  teamMembers = [],
  equipment = []
}: JobFormModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'other' as JobType,
    status: 'draft' as JobStatus,
    priority: 'medium' as JobPriority,
    clientName: '',
    clientContactPerson: '',
    clientPhone: '',
    clientEmail: '',
    locationName: '',
    locationAddress: '',
    locationLatitude: '',
    locationLongitude: '',
    siteInstructions: '',
    startDate: '',
    startTime: '',
    endDate: '',
    endTime: '',
    estimatedHours: '',
    budgetEstimated: '',
    budgetCurrency: 'USD',
    teamLeadId: '',
    specialInstructions: ''
  })

  const [selectedOperators, setSelectedOperators] = useState<string[]>([])
  const [selectedEquipment, setSelectedEquipment] = useState<string[]>([])
  const [requiredSkills, setRequiredSkills] = useState<string[]>([])
  const [safetyRequirements, setSafetyRequirements] = useState<string[]>([])
  const [newSkill, setNewSkill] = useState('')
  const [newSafetyReq, setNewSafetyReq] = useState('')

  // Reset form when job changes
  useEffect(() => {
    if (job) {
      setFormData({
        title: job.title || '',
        description: job.description || '',
        type: job.type || 'other',
        status: job.status || 'draft',
        priority: job.priority || 'medium',
        clientName: job.client?.name || '',
        clientContactPerson: job.client?.contactPerson || '',
        clientPhone: job.client?.phone || '',
        clientEmail: job.client?.email || '',
        locationName: job.location?.name || '',
        locationAddress: job.location?.address || '',
        locationLatitude: job.location?.latitude?.toString() || '',
        locationLongitude: job.location?.longitude?.toString() || '',
        siteInstructions: job.location?.siteInstructions || '',
        startDate: job.schedule?.startDate ?
          (typeof job.schedule.startDate.toDate === 'function' ?
            new Date(job.schedule.startDate.toDate()).toISOString().split('T')[0] :
            new Date(job.schedule.startDate).toISOString().split('T')[0]
          ) : '',
        startTime: job.schedule?.startDate ?
          (typeof job.schedule.startDate.toDate === 'function' ?
            new Date(job.schedule.startDate.toDate()).toTimeString().slice(0, 5) :
            new Date(job.schedule.startDate).toTimeString().slice(0, 5)
          ) : '',
        endDate: job.schedule?.endDate ?
          (typeof job.schedule.endDate.toDate === 'function' ?
            new Date(job.schedule.endDate.toDate()).toISOString().split('T')[0] :
            new Date(job.schedule.endDate).toISOString().split('T')[0]
          ) : '',
        endTime: job.schedule?.endDate ?
          (typeof job.schedule.endDate.toDate === 'function' ?
            new Date(job.schedule.endDate.toDate()).toTimeString().slice(0, 5) :
            new Date(job.schedule.endDate).toTimeString().slice(0, 5)
          ) : '',
        estimatedHours: job.schedule?.estimatedHours?.toString() || '',
        budgetEstimated: job.budget?.estimated?.toString() || '',
        budgetCurrency: job.budget?.currency || 'USD',
        teamLeadId: job.assignments?.teamLeadId || '',
        specialInstructions: job.requirements?.specialInstructions || ''
      })
      setSelectedOperators(job.assignments?.operatorIds || [])
      setSelectedEquipment(job.assignments?.equipmentIds || [])
      setRequiredSkills(job.requirements?.requiredSkills || [])
      setSafetyRequirements(job.requirements?.safetyRequirements || [])
    } else {
      // Reset form for new job
      setFormData({
        title: '',
        description: '',
        type: 'other',
        status: 'draft',
        priority: 'medium',
        clientName: '',
        clientContactPerson: '',
        clientPhone: '',
        clientEmail: '',
        locationName: '',
        locationAddress: '',
        locationLatitude: '',
        locationLongitude: '',
        siteInstructions: '',
        startDate: '',
        startTime: '',
        endDate: '',
        endTime: '',
        estimatedHours: '',
        budgetEstimated: '',
        budgetCurrency: 'USD',
        teamLeadId: '',
        specialInstructions: ''
      })
      setSelectedOperators([])
      setSelectedEquipment([])
      setRequiredSkills([])
      setSafetyRequirements([])
    }
  }, [job])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addSkill = () => {
    if (newSkill.trim() && !requiredSkills.includes(newSkill.trim())) {
      setRequiredSkills(prev => [...prev, newSkill.trim()])
      setNewSkill('')
    }
  }

  const removeSkill = (skill: string) => {
    setRequiredSkills(prev => prev.filter(s => s !== skill))
  }

  const addSafetyRequirement = () => {
    if (newSafetyReq.trim() && !safetyRequirements.includes(newSafetyReq.trim())) {
      setSafetyRequirements(prev => [...prev, newSafetyReq.trim()])
      setNewSafetyReq('')
    }
  }

  const removeSafetyRequirement = (req: string) => {
    setSafetyRequirements(prev => prev.filter(r => r !== req))
  }

  const toggleOperator = (operatorId: string) => {
    setSelectedOperators(prev => 
      prev.includes(operatorId) 
        ? prev.filter(id => id !== operatorId)
        : [...prev, operatorId]
    )
  }

  const toggleEquipment = (equipmentId: string) => {
    setSelectedEquipment(prev => 
      prev.includes(equipmentId) 
        ? prev.filter(id => id !== equipmentId)
        : [...prev, equipmentId]
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // Create start and end timestamps
      const startDateTime = formData.startDate && formData.startTime 
        ? new Date(`${formData.startDate}T${formData.startTime}`)
        : new Date()
      
      const endDateTime = formData.endDate && formData.endTime 
        ? new Date(`${formData.endDate}T${formData.endTime}`)
        : new Date(startDateTime.getTime() + (parseInt(formData.estimatedHours) || 1) * 60 * 60 * 1000)

      const jobData: Partial<Job> = {
        title: formData.title,
        description: formData.description,
        type: formData.type,
        status: formData.status,
        priority: formData.priority,
        client: {
          name: formData.clientName,
          contactPerson: formData.clientContactPerson,
          phone: formData.clientPhone,
          email: formData.clientEmail
        },
        location: {
          name: formData.locationName,
          address: formData.locationAddress,
          latitude: formData.locationLatitude ? parseFloat(formData.locationLatitude) : undefined,
          longitude: formData.locationLongitude ? parseFloat(formData.locationLongitude) : undefined,
          siteInstructions: formData.siteInstructions
        },
        schedule: {
          startDate: Timestamp.fromDate(startDateTime),
          endDate: Timestamp.fromDate(endDateTime),
          estimatedHours: parseInt(formData.estimatedHours) || 0
        },
        budget: {
          estimated: parseFloat(formData.budgetEstimated) || 0,
          currency: formData.budgetCurrency
        },
        assignments: {
          teamLeadId: formData.teamLeadId || undefined,
          operatorIds: selectedOperators,
          equipmentIds: selectedEquipment
        },
        requirements: {
          requiredSkills,
          safetyRequirements,
          specialInstructions: formData.specialInstructions
        }
      }

      await onSubmit(jobData)
      onClose()
    } catch (error) {
      console.error('Error submitting job:', error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{job ? 'Edit Job' : 'Create New Job'}</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title">Job Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="type">Job Type</Label>
                <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="maintenance">Maintenance</SelectItem>
                    <SelectItem value="construction">Construction</SelectItem>
                    <SelectItem value="repair">Repair</SelectItem>
                    <SelectItem value="inspection">Inspection</SelectItem>
                    <SelectItem value="emergency">Emergency</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="on_hold">On Hold</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Client Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Client Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientName">Client Name</Label>
                <Input
                  id="clientName"
                  value={formData.clientName}
                  onChange={(e) => handleInputChange('clientName', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="clientContactPerson">Contact Person</Label>
                <Input
                  id="clientContactPerson"
                  value={formData.clientContactPerson}
                  onChange={(e) => handleInputChange('clientContactPerson', e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientPhone">Phone</Label>
                <Input
                  id="clientPhone"
                  value={formData.clientPhone}
                  onChange={(e) => handleInputChange('clientPhone', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="clientEmail">Email</Label>
                <Input
                  id="clientEmail"
                  type="email"
                  value={formData.clientEmail}
                  onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Location</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="locationName">Location Name *</Label>
                <Input
                  id="locationName"
                  value={formData.locationName}
                  onChange={(e) => handleInputChange('locationName', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="locationAddress">Address</Label>
                <Input
                  id="locationAddress"
                  value={formData.locationAddress}
                  onChange={(e) => handleInputChange('locationAddress', e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="locationLatitude">Latitude</Label>
                <Input
                  id="locationLatitude"
                  type="number"
                  step="any"
                  value={formData.locationLatitude}
                  onChange={(e) => handleInputChange('locationLatitude', e.target.value)}
                  placeholder="33.8366"
                />
              </div>
              <div>
                <Label htmlFor="locationLongitude">Longitude</Label>
                <Input
                  id="locationLongitude"
                  type="number"
                  step="any"
                  value={formData.locationLongitude}
                  onChange={(e) => handleInputChange('locationLongitude', e.target.value)}
                  placeholder="-117.9143"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="siteInstructions">Site Instructions</Label>
              <Textarea
                id="siteInstructions"
                value={formData.siteInstructions}
                onChange={(e) => handleInputChange('siteInstructions', e.target.value)}
                rows={2}
                placeholder="Special instructions for accessing the job site..."
              />
            </div>
          </div>

          {/* Schedule & Budget */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Schedule & Budget</h3>
            <div className="grid grid-cols-4 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => handleInputChange('startDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={formData.startTime}
                  onChange={(e) => handleInputChange('startTime', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="endDate">End Date</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => handleInputChange('endDate', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="endTime">End Time</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={formData.endTime}
                  onChange={(e) => handleInputChange('endTime', e.target.value)}
                />
              </div>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="estimatedHours">Estimated Hours</Label>
                <Input
                  id="estimatedHours"
                  type="number"
                  value={formData.estimatedHours}
                  onChange={(e) => handleInputChange('estimatedHours', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="budgetEstimated">Estimated Budget</Label>
                <Input
                  id="budgetEstimated"
                  type="number"
                  step="0.01"
                  value={formData.budgetEstimated}
                  onChange={(e) => handleInputChange('budgetEstimated', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="budgetCurrency">Currency</Label>
                <Select value={formData.budgetCurrency} onValueChange={(value) => handleInputChange('budgetCurrency', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="GBP">GBP</SelectItem>
                    <SelectItem value="CAD">CAD</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Team Assignments */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Team Assignments</h3>
            <div>
              <Label htmlFor="teamLeadId">Team Lead</Label>
              <Select value={formData.teamLeadId} onValueChange={(value) => handleInputChange('teamLeadId', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select team lead" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No team lead</SelectItem>
                  {teamMembers.map((member) => (
                    <SelectItem key={member.id} value={member.id}>
                      {member.firstName} {member.lastName} - {member.position}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Assigned Operators</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded p-2">
                {teamMembers.map((member) => (
                  <div key={member.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`operator-${member.id}`}
                      checked={selectedOperators.includes(member.id)}
                      onChange={() => toggleOperator(member.id)}
                    />
                    <label htmlFor={`operator-${member.id}`} className="text-sm">
                      {member.firstName} {member.lastName}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <Label>Assigned Equipment</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto border rounded p-2">
                {equipment.filter(eq => eq.status === 'available').map((eq) => (
                  <div key={eq.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`equipment-${eq.id}`}
                      checked={selectedEquipment.includes(eq.id)}
                      onChange={() => toggleEquipment(eq.id)}
                    />
                    <label htmlFor={`equipment-${eq.id}`} className="text-sm">
                      {eq.name} ({eq.type})
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Requirements */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Requirements</h3>

            <div>
              <Label>Required Skills</Label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={newSkill}
                  onChange={(e) => setNewSkill(e.target.value)}
                  placeholder="Add required skill..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                />
                <Button type="button" onClick={addSkill} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {requiredSkills.map((skill) => (
                  <Badge key={skill} variant="secondary" className="flex items-center gap-1">
                    {skill}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeSkill(skill)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <Label>Safety Requirements</Label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={newSafetyReq}
                  onChange={(e) => setNewSafetyReq(e.target.value)}
                  placeholder="Add safety requirement..."
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSafetyRequirement())}
                />
                <Button type="button" onClick={addSafetyRequirement} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {safetyRequirements.map((req) => (
                  <Badge key={req} variant="secondary" className="flex items-center gap-1">
                    {req}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => removeSafetyRequirement(req)}
                    />
                  </Badge>
                ))}
              </div>
            </div>

            <div>
              <Label htmlFor="specialInstructions">Special Instructions</Label>
              <Textarea
                id="specialInstructions"
                value={formData.specialInstructions}
                onChange={(e) => handleInputChange('specialInstructions', e.target.value)}
                rows={3}
                placeholder="Any special instructions or requirements for this job..."
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : job ? 'Update Job' : 'Create Job'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
